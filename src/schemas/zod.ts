import { z } from "zod";

export const LoginSchema = z.object({
  email: z.string().email({ message: "Email tidak valid" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
});

export const ProductSchema = z.object({
  name: z.string().min(1, { message: "Nama produk wajib diisi" }),
  description: z.string().optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  // Use coerce for string inputs from forms that should be numbers
  price: z.coerce
    .number({ invalid_type_error: "Harga jual satuan harus berupa angka" })
    .nonnegative({ message: "Harga jual satuan tidak boleh negatif" })
    .default(0),
  wholesalePrice: z.coerce
    .number({ invalid_type_error: "Harga grosir harus berupa angka" })
    .nonnegative({ message: "Harga grosir tidak boleh negatif" })
    .optional(),
  discountPrice: z.coerce
    .number({ invalid_type_error: "Harga diskon harus berupa angka" })
    .nonnegative({ message: "Harga diskon tidak boleh negatif" })
    .optional(),
  cost: z.coerce
    .number({ invalid_type_error: "Harga beli harus berupa angka" })
    .nonnegative({ message: "Harga beli tidak boleh negatif" })
    .default(0),
  stock: z.coerce
    .number({ invalid_type_error: "Stok harus berupa angka" })
    .int({ message: "Stok harus berupa bilangan bulat" })
    .nonnegative({ message: "Stok tidak boleh negatif" })
    .default(0),
  image: z.string().optional().default(""),
  // Individual tax rates for each price type
  salePriceTaxRate: z.coerce
    .number({
      invalid_type_error: "Tarif pajak harga jual satuan harus berupa angka",
    })
    .min(0, { message: "Tarif pajak tidak boleh negatif" })
    .max(100, { message: "Tarif pajak tidak boleh lebih dari 100%" })
    .default(0),
  wholesalePriceTaxRate: z.coerce
    .number({
      invalid_type_error: "Tarif pajak harga grosir harus berupa angka",
    })
    .min(0, { message: "Tarif pajak tidak boleh negatif" })
    .max(100, { message: "Tarif pajak tidak boleh lebih dari 100%" })
    .default(0),
  discountPriceTaxRate: z.coerce
    .number({
      invalid_type_error: "Tarif pajak harga diskon harus berupa angka",
    })
    .min(0, { message: "Tarif pajak tidak boleh negatif" })
    .max(100, { message: "Tarif pajak tidak boleh lebih dari 100%" })
    .default(0),
  costPriceTaxRate: z.coerce
    .number({ invalid_type_error: "Tarif pajak harga beli harus berupa angka" })
    .min(0, { message: "Tarif pajak tidak boleh negatif" })
    .max(100, { message: "Tarif pajak tidak boleh lebih dari 100%" })
    .default(0),
  weight: z.coerce
    .number({ invalid_type_error: "Berat harus berupa angka" })
    .nonnegative({ message: "Berat tidak boleh negatif" })
    .optional(),
  length: z.coerce
    .number({ invalid_type_error: "Panjang harus berupa angka" })
    .nonnegative({ message: "Panjang tidak boleh negatif" })
    .optional(),
  width: z.coerce
    .number({ invalid_type_error: "Lebar harus berupa angka" })
    .nonnegative({ message: "Lebar tidak boleh negatif" })
    .optional(),
  height: z.coerce
    .number({ invalid_type_error: "Tinggi harus berupa angka" })
    .nonnegative({ message: "Tinggi tidak boleh negatif" })
    .optional(),
  unit: z.string().default("Pcs"),
  tags: z
    .array(z.string())
    .max(3, { message: "Maksimal 3 tag yang diperbolehkan" })
    .optional()
    .default([]),
  isDraft: z.boolean().default(false),
});

// Schema for sale item (individual product in a sale)
export const SaleItemSchema = z.object({
  productId: z.string().min(1, { message: "Produk wajib dipilih" }),
  quantity: z.coerce
    .number({ invalid_type_error: "Jumlah harus berupa angka" })
    .int({ message: "Jumlah harus berupa bilangan bulat" })
    .positive({ message: "Jumlah harus lebih dari 0" }),
  priceAtSale: z.coerce
    .number({ invalid_type_error: "Harga jual harus berupa angka" })
    .positive({ message: "Harga jual harus positif" }),
});

// Schema for the entire sale
export const SaleSchema = z.object({
  items: z
    .array(SaleItemSchema)
    .min(1, { message: "Minimal satu produk harus dipilih" }),
  totalAmount: z.coerce
    .number({ invalid_type_error: "Total harus berupa angka" })
    .positive({ message: "Total harus positif" }),
  transactionNumber: z.string().optional(),
  invoiceRef: z.string().optional(),
  isDraft: z.boolean().default(false),
  // Customer relationship
  customerId: z.string().min(1, { message: "Pelanggan wajib dipilih" }),
  // Additional fields similar to purchases
  customerRefNumber: z.string().optional(),
  shippingAddress: z.string().optional(),
  paymentDueDate: z.date().optional(),
  paymentTerms: z.string().optional(),
  warehouse: z.string().optional(),
  tags: z.array(z.string()).optional().default([]),
  memo: z.string().optional(),
  lampiran: z
    .array(
      z.object({
        url: z.string(),
        filename: z.string(),
      })
    )
    .optional()
    .default([]),
  priceIncludesTax: z.boolean().default(false),
});

// Schema for purchase item (individual product in a purchase)
export const PurchaseItemSchema = z.object({
  productId: z.string().min(1, { message: "Produk wajib dipilih" }),
  quantity: z.coerce
    .number({ invalid_type_error: "Jumlah harus berupa angka" })
    .int({ message: "Jumlah harus berupa bilangan bulat" })
    .positive({ message: "Jumlah harus lebih dari 0" }),
  costAtPurchase: z.coerce
    .number({ invalid_type_error: "Harga beli harus berupa angka" })
    .positive({ message: "Harga beli harus positif" }),
  unit: z.string().optional().default("Buah"),
  tax: z.string().optional(),
});

// Schema for the entire purchase
export const PurchaseSchema = z.object({
  items: z
    .array(PurchaseItemSchema)
    .min(1, { message: "Minimal satu produk harus dipilih" }),
  totalAmount: z.coerce
    .number({ invalid_type_error: "Total harus berupa angka" })
    .positive({ message: "Total harus positif" }),
  invoiceRef: z.string().optional(),
  supplierId: z.string().min(1, { message: "Supplier wajib dipilih" }),
  isDraft: z.boolean().default(false),
  // New fields
  supplierEmail: z
    .string()
    .min(1, { message: "Email supplier wajib diisi" })
    .email({ message: "Email tidak valid" }),
  transactionDate: z.date().default(() => new Date()),
  paymentDueDate: z.date().optional(),
  transactionNumber: z.string().optional(),
  tags: z.array(z.string()).optional().default([]),
  billingAddress: z.string().optional(),
  warehouse: z.string().optional(), // This is used in the UI but not stored in the database

  memo: z.string().optional(),
  lampiran: z
    .array(
      z.object({
        url: z.string(),
        filename: z.string(),
      })
    )
    .optional()
    .default([]),
});

export const CustomerSchema = z.object({
  // Basic Info
  name: z.string().min(1, { message: "Nama pelanggan wajib diisi" }),
  firstName: z.string().optional(),
  middleName: z.string().optional(),
  lastName: z.string().optional(),
  contactName: z.string().optional(),

  // Contact Information
  phone: z.string().optional(),
  telephone: z.string().optional(),
  fax: z.string().optional(),
  email: z
    .string()
    .email({ message: "Email tidak valid" })
    .optional()
    .or(z.literal("")),

  // Identity Information
  identityType: z.string().optional(),
  identityNumber: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),

  // Company Information
  companyName: z.string().optional(),
  otherInfo: z.string().optional(),

  // Address Information
  address: z.string().optional(), // Keep for backward compatibility
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  sameAsShipping: z.boolean().default(false),

  // Bank Information
  bankName: z.string().optional(),
  bankBranch: z.string().optional(),
  accountHolder: z.string().optional(),
  accountNumber: z.string().optional(),

  // Additional fields
  notes: z.string().optional(),
});

export const SupplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, { message: "Nama supplier wajib diisi" }),
  firstName: z.string().optional(),
  middleName: z.string().optional(),
  lastName: z.string().optional(),
  contactName: z.string().optional(),

  // Contact Information
  phone: z.string().optional(),
  telephone: z.string().optional(),
  fax: z.string().optional(),
  email: z
    .string()
    .email({ message: "Email tidak valid" })
    .optional()
    .or(z.literal("")),

  // Identity Information
  identityType: z.string().optional(),
  identityNumber: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),

  // Company Information
  companyName: z.string().optional(),
  otherInfo: z.string().optional(),

  // Address Information
  address: z.string().optional(), // Keep for backward compatibility
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  sameAsShipping: z.boolean().default(false),

  // Bank Information
  bankName: z.string().optional(),
  bankBranch: z.string().optional(),
  accountHolder: z.string().optional(),
  accountNumber: z.string().optional(),

  // Additional fields
  notes: z.string().optional(),
});

export const DaftarSchema = z.object({
  username: z.string().min(1, { message: "Username wajib diisi" }),
  email: z.string().email({ message: "Email tidak valid" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
});

export const EmployeeLoginSchema = z.object({
  companyUsername: z
    .string()
    .min(1, { message: "Username perusahaan wajib diisi" }),
  employeeId: z.string().min(1, { message: "ID karyawan wajib diisi" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
});

export const CreateEmployeeSchema = z.object({
  name: z.string().min(1, { message: "Nama karyawan wajib diisi" }),
  employeeId: z.string().min(1, { message: "ID karyawan wajib diisi" }),
  password: z
    .string()
    .min(1, { message: "Password wajib diisi" })
    .min(6, "Password minimal 6 karakter"),
  role: z.enum(["ADMIN", "CASHIER"], {
    required_error: "Role wajib dipilih",
    invalid_type_error: "Role tidak valid",
  }),
});

// Schema for updating employee information
export const UpdateEmployeeNameSchema = z.object({
  name: z.string().min(1, { message: "Nama karyawan wajib diisi" }),
  role: z.enum(["ADMIN", "CASHIER"], {
    required_error: "Role wajib dipilih",
    invalid_type_error: "Role tidak valid",
  }),
});

// Schema for updating employee password
export const UpdateEmployeePasswordSchema = z
  .object({
    password: z
      .string()
      .min(1, { message: "Password wajib diisi" })
      .min(6, "Password minimal 6 karakter"),
    confirmPassword: z
      .string()
      .min(1, { message: "Konfirmasi password wajib diisi" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Password dan konfirmasi password tidak sama",
    path: ["confirmPassword"],
  });

// Warehouse schemas
export const WarehouseSchema = z.object({
  name: z.string().min(1, { message: "Nama gudang wajib diisi" }),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  contactName: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

export const StockTransferSchema = z.object({
  productId: z.string().min(1, { message: "Produk wajib dipilih" }),
  fromWarehouseId: z.string().min(1, { message: "Gudang asal wajib dipilih" }),
  toWarehouseId: z.string().min(1, { message: "Gudang tujuan wajib dipilih" }),
  quantity: z.coerce
    .number({ invalid_type_error: "Jumlah harus berupa angka" })
    .int({ message: "Jumlah harus berupa bilangan bulat" })
    .positive({ message: "Jumlah harus lebih dari 0" }),
  notes: z.string().optional(),
});

export const StockAdjustmentSchema = z.object({
  productId: z.string().min(1, { message: "Produk wajib dipilih" }),
  warehouseId: z.string().min(1, { message: "Gudang wajib dipilih" }),
  newQuantity: z.coerce
    .number({ invalid_type_error: "Jumlah stok harus berupa angka" })
    .int({ message: "Jumlah stok harus berupa bilangan bulat" })
    .min(0, { message: "Jumlah stok tidak boleh negatif" }),
  notes: z.string().optional(),
});
