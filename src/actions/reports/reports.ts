"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { startOfDay, endOfDay, subDays, format } from "date-fns";

// Interface for advanced filters
interface AdvancedFilters {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

// Helper function to get date range based on selection
const getDateRange = (dateRange: string) => {
  const now = new Date();
  let startDate: Date;
  let endDate: Date = endOfDay(now);

  switch (dateRange) {
    case "today":
      startDate = startOfDay(now);
      break;
    case "7d":
      startDate = startOfDay(subDays(now, 7));
      break;
    case "30d":
      startDate = startOfDay(subDays(now, 30));
      break;
    case "month":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case "year":
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      // Default to 30 days
      startDate = startOfDay(subDays(now, 30));
  }

  return { startDate, endDate };
};

// Function to get purchase report data
export const getPurchaseReportData = async (dateRange: string) => {
  try {
    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();

    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    // Fetch purchases within the date range
    const purchases = await db.purchase.findMany({
      where: {
        userId: effectiveUserId,
        purchaseDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        supplier: {
          select: {
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        purchaseDate: "desc",
      },
    });

    // Transform the data to match the expected format
    const purchaseData = purchases.map((purchase) => {
      // Count total items
      const totalItems = purchase.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      );

      return {
        id: purchase.transactionNumber || purchase.id,
        purchaseDate: purchase.purchaseDate.toISOString(),
        invoiceRef: purchase.invoiceRef || "-",
        paymentDueDate: purchase.paymentDueDate
          ? purchase.paymentDueDate.toISOString()
          : "-",
        supplier: {
          name: purchase.supplier?.name || "Tidak ada supplier",
        },
        totalAmount: purchase.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: purchase.purchaseDate.toISOString(),
        total: purchase.totalAmount.toNumber(),
        transactionNumber: purchase.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: purchaseData,
    };
  } catch (error) {
    console.error("Error fetching purchase report data:", error);
    return {
      error: "Gagal mengambil data laporan pembelian.",
    };
  }
};

// Function to get sales report data
export const getSalesReportData = async (dateRange: string) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    const sales = await db.sale.findMany({
      where: {
        userId: effectiveUserId,
        saleDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        items: true, // Include the full items relation
        customer: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        saleDate: "desc",
      },
    });

    const salesData = sales.map((sale) => {
      const totalItems = sale.items.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );
      return {
        id: sale.transactionNumber || sale.id,
        saleDate: sale.saleDate.toISOString(),
        invoiceRef: sale.invoiceRef || "-",
        paymentDueDate: sale.paymentDueDate
          ? sale.paymentDueDate.toISOString()
          : "-",
        customer: {
          name: sale.customer?.name || "Pelanggan Umum",
        },
        totalAmount: sale.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: sale.saleDate.toISOString(),
        total: sale.totalAmount.toNumber(),
        transactionNumber: sale.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: salesData,
    };
  } catch (error) {
    console.error("Error fetching sales report data:", error);
    return {
      error: "Gagal mengambil data laporan penjualan.",
    };
  }
};

// Function to get product report data
export const getProductReportData = async (dateRange: string) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    // Fetch all products for the user
    const products = await db.product.findMany({
      where: {
        userId: effectiveUserId,
      },
      include: {
        category: {
          select: { name: true },
        },
        variants: {
          select: {
            colorName: true,
          },
        },
        saleItems: {
          where: {
            sale: {
              saleDate: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
          select: {
            quantity: true,
            priceAtSale: true,
          },
        },
        // We might need purchaseItems if we want to calculate profit based on costAtPurchase
        // purchaseItems: {
        //   where: { purchase: { purchaseDate: { gte: startDate, lte: endDate } } },
        //   select: { quantity: true, costAtPurchase: true }
        // }
      },
      orderBy: {
        id: "asc", // Sort by product ID in ascending order (000001, 000002, etc.)
      },
    });

    const productData = products.map((product) => {
      const totalSold = product.saleItems.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );
      const totalRevenue = product.saleItems.reduce(
        (sum: number, item: { quantity: number; priceAtSale: any }) =>
          sum + item.quantity * item.priceAtSale.toNumber(),
        0
      );
      // Basic profit calculation (Revenue - Cost * Sold). Requires cost on Product model.
      const totalCost = product.cost ? product.cost.toNumber() * totalSold : 0;
      const totalProfit = totalRevenue - totalCost;

      // Determine stock status
      const stockStatus =
        product.stock <= 0
          ? "Habis"
          : product.stock <= 10
            ? "Stok Rendah"
            : "Tersedia";

      return {
        id: product.id,
        name: product.name,
        description: product.description || "", // Add description field
        sku: product.sku || "-",
        barcode: product.barcode || "-", // Use actual barcode from database
        unit: product.unit || "pcs",
        stock: product.stock,
        cost: product.cost ? product.cost.toNumber() : 0,
        price: product.price ? product.price.toNumber() : 0,
        wholesalePrice: product.wholesalePrice
          ? product.wholesalePrice.toNumber()
          : 0,
        discountPrice: product.discountPrice
          ? product.discountPrice.toNumber()
          : 0,
        category: {
          name: product.category?.name || "Tidak ada kategori",
        },
        tags: product.tags || [], // Use actual tags from database
        variants: product.variants || [],
        stockStatus: stockStatus,
        // Keep legacy fields for backward compatibility
        sold: totalSold,
        revenue: totalRevenue,
        profit: totalProfit, // This depends on having a 'cost' field on the Product model
      };
    });

    return {
      success: true,
      data: productData,
    };
  } catch (error) {
    console.error("Error fetching product report data:", error);
    return {
      error: "Gagal mengambil data laporan produk.",
    };
  }
};

// Function to get purchase chart data
export const getPurchaseChartData = async (dateRange: string) => {
  try {
    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();

    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    // Fetch purchases within the date range
    const purchases = await db.purchase.findMany({
      where: {
        userId: effectiveUserId,
        purchaseDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        supplier: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        purchaseDate: "asc",
      },
    });

    // Group purchases by month for trend chart
    const monthlyData = new Map<string, number>();
    purchases.forEach((purchase) => {
      const month = format(purchase.purchaseDate, "MMM");
      const currentTotal = monthlyData.get(month) || 0;
      monthlyData.set(month, currentTotal + purchase.totalAmount.toNumber());
    });

    const trendData = Array.from(monthlyData.entries()).map(
      ([name, total]) => ({
        name,
        total,
      })
    );

    // Group purchases by supplier for pie chart
    const supplierData = new Map<string, number>();
    purchases.forEach((purchase) => {
      const supplierName = purchase.supplier?.name || "Tidak ada supplier";
      const currentTotal = supplierData.get(supplierName) || 0;
      supplierData.set(
        supplierName,
        currentTotal + purchase.totalAmount.toNumber()
      );
    });

    const supplierChartData = Array.from(supplierData.entries()).map(
      ([name, value]) => ({
        name,
        value,
      })
    );

    return {
      success: true,
      data: {
        trendData,
        supplierData: supplierChartData,
      },
    };
  } catch (error) {
    console.error("Error fetching purchase chart data:", error);
    return {
      error: "Gagal mengambil data grafik pembelian.",
    };
  }
};

// Enhanced function to get purchase report data with advanced filters
export const getPurchaseReportDataWithFilters = async (
  filters: AdvancedFilters
) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause with advanced filters
    const whereClause: any = {
      userId: effectiveUserId,
      purchaseDate: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add supplier filter if provided
    if (filters.supplier) {
      whereClause.supplier = {
        name: {
          contains: filters.supplier,
          mode: "insensitive",
        },
      };
    }

    // Add status filter if provided (assuming you have a status field)
    if (filters.status) {
      whereClause.status = filters.status;
    }

    const purchases = await db.purchase.findMany({
      where: whereClause,
      include: {
        supplier: {
          select: {
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
                category: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        purchaseDate: "desc",
      },
    });

    // Filter by category if provided (filter at application level since it's nested)
    let filteredPurchases = purchases;
    if (filters.category) {
      filteredPurchases = purchases.filter((purchase) =>
        purchase.items.some((item) =>
          item.product.category?.name
            ?.toLowerCase()
            .includes(filters.category!.toLowerCase())
        )
      );
    }

    const purchaseData = filteredPurchases.map((purchase) => {
      const totalItems = purchase.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      );

      return {
        id: purchase.transactionNumber || purchase.id,
        purchaseDate: purchase.purchaseDate.toISOString(),
        invoiceRef: purchase.invoiceRef || "-",
        paymentDueDate: purchase.paymentDueDate
          ? purchase.paymentDueDate.toISOString()
          : "-",
        supplier: {
          name: purchase.supplier?.name || "Tidak ada supplier",
        },
        totalAmount: purchase.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: purchase.purchaseDate.toISOString(),
        total: purchase.totalAmount.toNumber(),
        transactionNumber: purchase.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: purchaseData,
    };
  } catch (error) {
    console.error("Error fetching purchase report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan pembelian.",
    };
  }
};

// Enhanced function to get sales report data with advanced filters
export const getSalesReportDataWithFilters = async (
  filters: AdvancedFilters
) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause with advanced filters
    const whereClause: any = {
      userId: effectiveUserId,
      saleDate: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add customer filter if provided
    if (filters.customer) {
      whereClause.customer = {
        name: {
          contains: filters.customer,
          mode: "insensitive",
        },
      };
    }

    // Add status filter if provided
    if (filters.status) {
      whereClause.status = filters.status;
    }

    const sales = await db.sale.findMany({
      where: whereClause,
      include: {
        customer: {
          select: {
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
                category: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        saleDate: "desc",
      },
    });

    // Filter by category if provided (filter at application level since it's nested)
    let filteredSales = sales;
    if (filters.category) {
      filteredSales = sales.filter((sale) =>
        sale.items.some((item) =>
          item.product.category?.name
            ?.toLowerCase()
            .includes(filters.category!.toLowerCase())
        )
      );
    }

    const salesData = filteredSales.map((sale) => {
      const totalItems = sale.items.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );

      return {
        id: sale.transactionNumber || sale.id,
        saleDate: sale.saleDate.toISOString(),
        invoiceRef: sale.invoiceRef || "-",
        paymentDueDate: sale.paymentDueDate
          ? sale.paymentDueDate.toISOString()
          : "-",
        customer: {
          name: sale.customer?.name || "Pelanggan Umum",
        },
        totalAmount: sale.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: sale.saleDate.toISOString(),
        total: sale.totalAmount.toNumber(),
        transactionNumber: sale.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: salesData,
    };
  } catch (error) {
    console.error("Error fetching sales report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan penjualan.",
    };
  }
};

// Enhanced function to get product report data with advanced filters
export const getProductReportDataWithFilters = async (
  filters: AdvancedFilters
) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause for products
    const whereClause: any = {
      userId: effectiveUserId,
    };

    // Add category filter if provided
    if (filters.category) {
      whereClause.category = {
        name: {
          contains: filters.category,
          mode: "insensitive",
        },
      };
    }

    const products = await db.product.findMany({
      where: whereClause,
      include: {
        category: {
          select: { name: true },
        },
        variants: {
          select: {
            colorName: true,
          },
        },
        saleItems: {
          where: {
            sale: {
              saleDate: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
          select: {
            quantity: true,
            priceAtSale: true,
          },
        },
      },
      orderBy: {
        id: "asc", // Sort by product ID in ascending order (000001, 000002, etc.)
      },
    });

    const productData = products.map((product) => {
      const totalSold = product.saleItems.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );
      const totalRevenue = product.saleItems.reduce(
        (sum: number, item: { quantity: number; priceAtSale: any }) =>
          sum + item.quantity * item.priceAtSale.toNumber(),
        0
      );
      const totalCost = product.cost ? product.cost.toNumber() * totalSold : 0;
      const totalProfit = totalRevenue - totalCost;

      // Determine stock status
      const stockStatus =
        product.stock <= 0
          ? "Habis"
          : product.stock <= 10
            ? "Stok Rendah"
            : "Tersedia";

      return {
        id: product.id,
        name: product.name,
        description: product.description || "", // Add description field
        sku: product.sku || "-",
        barcode: product.barcode || "-", // Use actual barcode from database
        unit: product.unit || "pcs",
        stock: product.stock,
        cost: product.cost ? product.cost.toNumber() : 0,
        price: product.price ? product.price.toNumber() : 0,
        wholesalePrice: product.wholesalePrice
          ? product.wholesalePrice.toNumber()
          : 0,
        discountPrice: product.discountPrice
          ? product.discountPrice.toNumber()
          : 0,
        category: {
          name: product.category?.name || "Tidak ada kategori",
        },
        tags: product.tags || [], // Use actual tags from database
        variants: product.variants || [],
        stockStatus: stockStatus,
        // Keep legacy fields for backward compatibility
        sold: totalSold,
        revenue: totalRevenue,
        profit: totalProfit,
      };
    });

    return {
      success: true,
      data: productData,
    };
  } catch (error) {
    console.error("Error fetching product report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan produk.",
    };
  }
};

// Function to get customer report data
export const getCustomerReportData = async () => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const customers = await db.customer.findMany({
      where: {
        userId: effectiveUserId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const customerData = customers.map((customer) => ({
      id: customer.id,
      name: customer.name,
      contactName: customer.contactName || "-",
      email: customer.email || "-",
      phone: customer.phone || "-",
      address: customer.address || "-",
      NIK: customer.NIK || "-",
      NPWP: customer.NPWP || "-",
      notes: customer.notes || "-",
      createdAt: customer.createdAt.toISOString(),
      updatedAt: customer.updatedAt.toISOString(),
    }));

    return {
      success: true,
      data: customerData,
    };
  } catch (error) {
    console.error("Error fetching customer report data:", error);
    return {
      error: "Gagal mengambil data laporan pelanggan.",
    };
  }
};

// Function to get supplier report data
export const getSupplierReportData = async () => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const suppliers = await db.supplier.findMany({
      where: {
        userId: effectiveUserId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const supplierData = suppliers.map((supplier) => ({
      id: supplier.id,
      name: supplier.name,
      contactName: supplier.contactName || "-",
      email: supplier.email || "-",
      phone: supplier.phone || "-",
      address: supplier.address || "-",
      notes: supplier.notes || "-",
      createdAt: supplier.createdAt.toISOString(),
      updatedAt: supplier.updatedAt.toISOString(),
    }));

    return {
      success: true,
      data: supplierData,
    };
  } catch (error) {
    console.error("Error fetching supplier report data:", error);
    return {
      error: "Gagal mengambil data laporan supplier.",
    };
  }
};
