import React from "react";
import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { Role } from "@prisma/client";
import EventDiscountManagement from "@/components/pages/dashboard/event-discounts/EventDiscountManagement";

export const metadata: Metadata = {
  title: "Diskon Event | KivaPOS",
  description: "Kelola diskon event untuk produk Anda",
};

export default async function EventDiscountsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user has permission to access event discounts
  const userRole = session.user.role as Role;
  if (userRole !== Role.OWNER && userRole !== Role.ADMIN) {
    redirect("/dashboard");
  }

  return <EventDiscountManagement />;
}
