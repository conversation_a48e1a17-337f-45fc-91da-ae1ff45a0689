import React from "react";
import { notFound } from "next/navigation";
import SaleDetailPage from "@/components/pages/dashboard/sales/detail";
import { getSaleById } from "@/actions/entities/sales";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Detail Penjualan - KivaPOS",
  description: "Lihat detail transaksi penjualan",
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams?: Promise<Record<string, string | string[]>>;
};

export default async function SaleDetail(props: PageProps) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  // Fetch the sale with the given ID
  const saleResult = await getSaleById(id);

  // If sale not found, return 404
  if (!saleResult.sale) {
    notFound();
  }

  // The sale data is already serialized in getSaleById
  // Convert Date objects to strings and null to undefined for the component
  const serializedSale = {
    ...saleResult.sale,
    saleDate: saleResult.sale.saleDate.toISOString(),
    createdAt: saleResult.sale.createdAt.toISOString(),
    updatedAt: saleResult.sale.updatedAt.toISOString(),
    // Convert null values to undefined for TypeScript compatibility
    transactionNumber: saleResult.sale.transactionNumber || undefined,
    invoiceRef: saleResult.sale.invoiceRef || undefined,
    customerId: saleResult.sale.customerId || undefined,
    customerRefNumber: saleResult.sale.customerRefNumber || undefined,
    shippingAddress: saleResult.sale.shippingAddress || undefined,
    paymentDueDate: saleResult.sale.paymentDueDate || undefined,
    paymentTerms: saleResult.sale.paymentTerms || undefined,
    warehouseId: saleResult.sale.warehouseId || undefined,
    memo: saleResult.sale.memo || undefined,
    // Handle JSON fields properly
    tags: saleResult.sale.tags || [],
    lampiran:
      (saleResult.sale.lampiran as { url: string; filename: string }[]) || [],
    // Handle customer data properly - convert null to undefined
    customer: saleResult.sale.customer
      ? {
          id: saleResult.sale.customer.id,
          name: saleResult.sale.customer.name,
          email: saleResult.sale.customer.email || undefined,
          phone: saleResult.sale.customer.phone || undefined,
          NIK: saleResult.sale.customer.NIK || undefined,
          NPWP: saleResult.sale.customer.NPWP || undefined,
        }
      : undefined,
    items: saleResult.sale.items.map((item) => ({
      ...item,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    })),
  };

  return <SaleDetailPage sale={serializedSale} />;
}
