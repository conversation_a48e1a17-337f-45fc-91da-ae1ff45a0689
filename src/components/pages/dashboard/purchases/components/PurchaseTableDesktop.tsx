import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Purchase, ColumnVisibility } from "../types"; // Import from the types file
import { purchasesColumnConfig } from "../config/columnConfig";
import { Trash, MoreHorizontal, Edit, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { deletePurchase } from "@/actions/entities/purchases";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Use shared column configuration
const columnConfig = purchasesColumnConfig;

interface PurchaseTableDesktopProps {
  purchases: Purchase[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
  onSelectionChange?: (selectedIds: string[]) => void;
}

export const PurchaseTableDesktop: React.FC<PurchaseTableDesktopProps> = ({
  purchases,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
  onSelectionChange,
}) => {
  const router = useRouter();
  const [purchaseToDelete, setPurchaseToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedPurchases, setSelectedPurchases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Helper function to render cell content based on column key
  const renderCellContent = (
    purchase: Purchase,
    columnKey: keyof ColumnVisibility
  ) => {
    switch (columnKey) {
      case "id":
        return (
          <Link
            href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
            className="hover:underline text-blue-600 dark:text-blue-400 cursor-pointer"
          >
            {purchase.transactionNumber || purchase.id.substring(0, 8)}
          </Link>
        );
      case "date":
        return formatDate(purchase.purchaseDate);
      case "paymentDueDate":
        return purchase.paymentDueDate
          ? formatDate(purchase.paymentDueDate)
          : "-";
      case "supplier":
        return purchase.supplier?.name || "-";
      case "totalAmount":
        return `Rp ${purchase.totalAmount.toLocaleString("id-ID")}`;
      case "itemCount":
        return purchase.items.length;
      case "quantity":
        return purchase.items
          .reduce((sum, item) => sum + (item.quantity || 0), 0)
          .toLocaleString("id-ID");
      case "invoiceRef":
        return purchase.invoiceRef || "-";
      case "tags":
        return purchase.tags && purchase.tags.length > 0
          ? purchase.tags.join(", ")
          : "-";
      default:
        return "-";
    }
  };

  // Handle select all checkbox
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      const allIds = purchases.map((purchase) => purchase.id);
      setSelectedPurchases(allIds);
      if (onSelectionChange) onSelectionChange(allIds);
    } else {
      setSelectedPurchases([]);
      if (onSelectionChange) onSelectionChange([]);
    }
  };

  // Handle individual checkbox selection
  const handleSelectPurchase = (purchaseId: string, checked: boolean) => {
    let newSelected: string[];

    if (checked) {
      newSelected = [...selectedPurchases, purchaseId];
    } else {
      newSelected = selectedPurchases.filter((id) => id !== purchaseId);
      // If we're deselecting an item, also uncheck the "select all" checkbox
      if (selectAll) setSelectAll(false);
    }

    setSelectedPurchases(newSelected);
    if (onSelectionChange) onSelectionChange(newSelected);
  };

  // Handle delete purchase
  const handleDeletePurchase = async (id: string) => {
    setIsDeleting(true);
    try {
      const result = await deletePurchase(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting purchase:", error);
      toast.error("Terjadi kesalahan saat menghapus pembelian.");
    } finally {
      setIsDeleting(false);
      setPurchaseToDelete(null);
    }
  };

  // Format date for display in concise format (DD/MM/YYYY)
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, "0");
    const month = String(d.getMonth() + 1).padStart(2, "0"); // Month is 0-indexed
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  };
  return (
    <div className="relative overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-gray-700">
          <tr>
            {/* Checkbox column - always visible */}
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              <Checkbox
                checked={selectAll}
                onCheckedChange={handleSelectAll}
                aria-label="Select all purchases"
              />
            </th>
            {columnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 border-r border-gray-200 dark:border-gray-700"
                    onClick={() => handleSort(column.sortKey)}
                  >
                    <div className="flex items-center">
                      {column.label}
                      {getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {purchases.length > 0 ? (
            purchases.map((purchase) => (
              <tr
                key={purchase.id}
                className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {/* Checkbox cell - always visible */}
                <td className="px-4 py-4 border-r border-gray-200 dark:border-gray-700">
                  <Checkbox
                    checked={selectedPurchases.includes(purchase.id)}
                    onCheckedChange={(checked) =>
                      handleSelectPurchase(purchase.id, checked === true)
                    }
                    aria-label={`Select purchase ${purchase.id}`}
                  />
                </td>
                {columnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "id"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {renderCellContent(purchase, column.key)}
                      </td>
                    )
                )}
                <td className="px-6 py-4 text-right whitespace-nowrap border-l border-gray-200 dark:border-gray-700">
                  <div className="flex justify-end space-x-1">
                    {/* Action Dropdown */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`
                            )
                          }
                          className="cursor-pointer"
                        >
                          <span>Lihat Detail</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            toast.info("Fitur duplikasi akan segera hadir!")
                          }
                          className="cursor-pointer"
                        >
                          <span>Duplikasi</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            toast.info("Fitur cetak akan segera hadir!")
                          }
                          className="cursor-pointer"
                        >
                          <span>Cetak</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    {/* Share Button (WhatsApp) */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-green-500 hover:text-green-700 dark:text-green-400 dark:hover:text-green-200 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        const message = `Pembelian ${purchase.transactionNumber || purchase.id} - Total: ${new Intl.NumberFormat(
                          "id-ID",
                          {
                            style: "currency",
                            currency: "IDR",
                            minimumFractionDigits: 0,
                          }
                        ).format(purchase.totalAmount)}`;
                        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                        window.open(whatsappUrl, "_blank");
                      }}
                    >
                      <Share2 className="h-4 w-4" />
                      <span className="sr-only">Share via WhatsApp</span>
                    </Button>

                    {/* Edit Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() =>
                        router.push(`/dashboard/purchases/edit/${purchase.id}`)
                      }
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-200 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus pembelian ini?
                            Tindakan ini tidak dapat dibatalkan dan akan
                            mengurangi stok produk.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              setPurchaseToDelete(purchase.id);
                              handleDeletePurchase(purchase.id);
                            }}
                            disabled={
                              isDeleting && purchaseToDelete === purchase.id
                            }
                            className="bg-red-500 hover:bg-red-600"
                          >
                            {isDeleting && purchaseToDelete === purchase.id
                              ? "Menghapus..."
                              : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={
                  Object.values(columnVisibility).filter(Boolean).length + 2 // +2 for checkbox and actions columns
                }
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {searchTerm
                  ? "Tidak ada pembelian yang sesuai dengan pencarian."
                  : "Belum ada data pembelian."}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
