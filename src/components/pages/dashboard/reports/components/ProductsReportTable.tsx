"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { getProductReportData } from "@/actions/reports/reports";

interface ProductsReportTableProps {
  dateRange: string;
}

interface ProductData {
  id: string;
  name: string;
  sku: string;
  barcode: string;
  category: {
    name: string;
  };
  unit: string;
  stock: number;
  cost: number;
  price: number;
  wholesalePrice: number;
  discountPrice: number;
  sold: number;
  revenue: number;
  profit: number;
  stockStatus?: string; // Optional since we're not displaying it
  tags: any[];
  variants: any[];
}

// Define the interface for the handle exposed by useImperativeHandle
export interface ProductsReportTableHandle {
  exportData: () => {
    ID: string;
    "Nama Produk": string;
    SKU: string;
    Barcode: string;
    Kategori: string;
    Unit: string;
    Stok: number;
    "Harga Beli": number;
    "Harga Jual": number;
    "Harga Grosir": number;
    "Harga Diskon": number;
    Terjual: number;
    Pendapatan: number;
    Keuntungan: number;
  }[];
}

export const ProductsReportTable = React.forwardRef<
  ProductsReportTableHandle, // Use the handle interface here
  ProductsReportTableProps
>(({ dateRange }, ref) => {
  // Create a ref for the component's root div element
  const componentRef = React.useRef<HTMLDivElement>(null);

  // Expose the exportData function using useImperativeHandle
  // Note: We pass the ref directly here, not the resolvedRef which might be null initially
  React.useImperativeHandle(ref, () => ({
    exportData: () => {
      // Define exportData inside the callback to access the latest products state
      return products.map((product) => ({
        ID: product.id,
        "Nama Produk": product.name,
        SKU: product.sku,
        Barcode: product.barcode,
        Kategori: product.category.name,
        Unit: product.unit,
        Stok: product.stock,
        "Harga Beli": product.cost,
        "Harga Jual": product.price,
        "Harga Grosir": product.wholesalePrice,
        "Harga Diskon": product.discountPrice,
        Terjual: product.sold,
        Pendapatan: product.revenue,
        Keuntungan: product.profit,
      }));
    },
  }));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<ProductData[]>([]);
  const [sortField, setSortField] = useState<keyof ProductData>("sold");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // Fetch products data based on date range
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await getProductReportData(dateRange);

        if (result.error) {
          setError(result.error);
        } else if (result.data) {
          setProducts(result.data);
        }
      } catch (err) {
        setError("Gagal memuat data produk");
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [dateRange]);

  // Handle sorting
  const handleSort = (field: keyof ProductData) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Sort the data
  const sortedProducts = [...products].sort((a, b) => {
    if (
      sortField === "stock" ||
      sortField === "sold" ||
      sortField === "revenue" ||
      sortField === "profit" ||
      sortField === "cost" ||
      sortField === "price" ||
      sortField === "wholesalePrice" ||
      sortField === "discountPrice"
    ) {
      return sortDirection === "asc"
        ? a[sortField] - b[sortField]
        : b[sortField] - a[sortField];
    } else if (sortField === "category") {
      const aValue = a.category.name.toLowerCase();
      const bValue = b.category.name.toLowerCase();
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    } else {
      const aValue = String(a[sortField]).toLowerCase();
      const bValue = String(b[sortField]).toLowerCase();
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
  });

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get sort icon
  const getSortIcon = (field: keyof ProductData) => {
    if (field !== sortField) return null;
    return sortDirection === "asc" ? " ↑" : " ↓";
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    // Attach the componentRef to the root div
    <div
      id="products-report-table"
      ref={componentRef}
      className="rounded-md border"
    >
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("id")}
            >
              ID {getSortIcon("id")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("name")}
            >
              Nama Produk {getSortIcon("name")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("sku")}
            >
              SKU {getSortIcon("sku")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("barcode")}
            >
              Barcode {getSortIcon("barcode")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("category")}
            >
              Kategori {getSortIcon("category")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("stock")}
            >
              Stok {getSortIcon("stock")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("price")}
            >
              Harga Jual {getSortIcon("price")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("sold")}
            >
              Terjual {getSortIcon("sold")}
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => handleSort("revenue")}
            >
              Pendapatan {getSortIcon("revenue")}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedProducts.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center py-4">
                Tidak ada data produk untuk periode ini
              </TableCell>
            </TableRow>
          ) : (
            sortedProducts.map((product) => (
              <TableRow key={product.id}>
                <TableCell>{product.id}</TableCell>
                <TableCell>{product.name}</TableCell>
                <TableCell>{product.sku}</TableCell>
                <TableCell>{product.barcode}</TableCell>
                <TableCell>{product.category.name}</TableCell>
                <TableCell>{product.stock}</TableCell>
                <TableCell>{formatCurrency(product.price)}</TableCell>
                <TableCell>{product.sold}</TableCell>
                <TableCell>{formatCurrency(product.revenue)}</TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
});

// Add display name
ProductsReportTable.displayName = "ProductsReportTable";
