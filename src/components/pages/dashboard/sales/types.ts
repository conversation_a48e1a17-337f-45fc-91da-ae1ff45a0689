export interface SaleItem {
  id: string;
  quantity: number;
  priceAtSale: number;
  saleId: string;
  productId: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  product: {
    name: string;
  };
}

export interface Sale {
  id: string;
  saleDate: Date | string;
  totalAmount: number;
  transactionNumber?: string | null;
  invoiceRef?: string | null;
  isDraft: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  userId: string;
  user?: {
    id: string;
    name: string | null;
    username: string | null;
  };
  employeeId?: string | null;
  items: SaleItem[];
  // Customer relationship
  customerId?: string | null;
  customer?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    NIK?: string;
    NPWP?: string;
  };
  // Additional fields from schema
  customerRefNumber?: string;
  shippingAddress?: string;
  paymentDueDate?: Date | string;
  paymentTerms?: string;
  warehouse?: string;
  tags?: string[];
  memo?: string;
  lampiran?: { url: string; filename: string }[];
  priceIncludesTax?: boolean;
}

export interface SaleCounts {
  total: number;
  today: number;
  thisMonth: number;
  pending: number;
  drafts: number;
}

export interface ColumnVisibility {
  id: boolean;
  date: boolean;
  paymentDueDate: boolean;
  customer: boolean;
  totalAmount: boolean;
  itemCount: boolean;
  invoiceRef: boolean;
  tags: boolean;
  totalQuantity: boolean; // New column for sum of quantity
}
