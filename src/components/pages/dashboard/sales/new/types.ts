import { z } from "zod";
import { SaleSchema } from "@/schemas/zod";

// Define the item schema with additional fields
const SaleItemSchema = z.object({
  productId: z.string(),
  quantity: z.number().min(1).default(1),
  priceAtSale: z.number().nonnegative().default(0),
  discount: z.string().optional().default("0"),
  unit: z.string().default("Buah"),
  tax: z.string().optional(),
});

// Extend the base SaleSchema with additional fields for the enhanced UI
export const EnhancedSaleSchema = SaleSchema.extend({
  // Additional UI-specific fields not in base schema
  customerEmail: z
    .string()
    .min(1, { message: "Email pelanggan wajib diisi" })
    .email({ message: "Email tidak valid" }),
  customerNIK: z.string().optional(),
  customerNPWP: z.string().optional(),
  paymentMethod: z.string().default("cash"),
  amountPaid: z.coerce.number().nonnegative().optional(),
  notes: z.string().optional(),
  // New fields similar to purchases
  transactionDate: z.date().default(() => new Date()),
  isDraft: z.boolean().default(false),
  items: z.array(SaleItemSchema),
});

// Define the type for the form values
export type SaleFormValues = z.infer<typeof EnhancedSaleSchema>;

// Define the type for products specific to sales
export interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  image?: string;
  createdAt?: Date | string;
}

// Define the type for customers
export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  NIK?: string;
  NPWP?: string;
}
