"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Package, CreditCard, FileText } from "lucide-react";
import { SaleFormValues, Product } from "../types";
import { Control, UseFieldArrayRemove } from "react-hook-form";
import SaleInfoSection from "./SaleInfoSection";
import SaleItemTable from "./SaleItemTable";
import SalePaymentSection from "./SalePaymentSection";
import AdditionalInfo from "./AdditionalInfo";
import { Separator } from "@/components/ui/separator";

interface CombinedSaleFormProps {
  control: Control<SaleFormValues>;
  isPending: boolean;
  products: Product[];
  items: SaleFormValues["items"];
  fields: any[];
  append: (value: any) => void;
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
  totalAmount: number;
  createdAt?: string; // Optional createdAt for edit mode
  setValue?: (name: keyof SaleFormValues, value: any) => void; // Optional setValue function
  trigger?: (
    name?: keyof SaleFormValues | (keyof SaleFormValues)[]
  ) => Promise<boolean>; // Optional trigger function
}

const CombinedSaleForm: React.FC<CombinedSaleFormProps> = ({
  control,
  isPending,
  products,
  items,
  fields,
  append,
  remove,
  handleProductChange,
  totalAmount,
  createdAt,
  setValue,
  trigger,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Formulir Penjualan</CardTitle>
        <CardDescription>
          Lengkapi semua informasi penjualan di bawah ini
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Sale Information Section */}
        <div>
          <SaleInfoSection
            control={control}
            isPending={isPending}
            createdAt={createdAt}
            setValue={setValue}
            trigger={trigger}
          />
        </div>

        <Separator />

        {/* Items Section */}
        <div className="my-2">
          <div className="flex items-center gap-2 mb-4">
            <Package className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Item Penjualan</h3>
          </div>
          <SaleItemTable
            control={control}
            isPending={isPending}
            products={products}
            items={items}
            fields={fields}
            append={append}
            remove={remove}
            handleProductChange={handleProductChange}
          />
        </div>

        <Separator />

        {/* Additional Info Section (Memo, Lampiran, Tags) */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <FileText className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Tambahan</h3>
          </div>
          <AdditionalInfo
            control={control}
            isPending={isPending}
            items={items}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default CombinedSaleForm;
