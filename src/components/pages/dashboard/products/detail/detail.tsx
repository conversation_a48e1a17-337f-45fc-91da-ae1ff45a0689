"use client";

import type { NextPage } from "next";
import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";

// Import our component modules
import ProductHeader from "../components/ProductHeader";
import ProductSummary from "../components/ProductSummary";
import ProductTabs from "../components/ProductTabs";
import ProductTimestamps from "../components/ProductTimestamps";
import ProductTransactionHistory from "../components/ProductTransactionHistory";

interface Category {
  id: string;
  name: string;
}

interface ProductVariant {
  id: string;
  sku: string | null;
  colorName: string;
  colorCode: string;
  price: number | null;
  stock: number;
  image: string | null;
  productId: string;
}

interface Product {
  id: string;
  name: string;
  description: string | null;
  sku: string | null;
  barcode: string | null;
  price: number;
  wholesalePrice: number | null;
  discountPrice: number | null; // Added discountPrice
  cost: number | null;
  stock: number;
  image: string | null;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string | null;
  category: Category | null;
  taxRate?: number;
  hasVariants?: boolean;
  trackInventory?: boolean;
  minStockLevel?: number;
  weight?: number | null;
  length?: number | null;
  width?: number | null;
  height?: number | null;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  tags?: string[];
  isDraft?: boolean;
  variants?: ProductVariant[];
  // Tax rates
  salePriceTaxRate?: number | null;
  wholesalePriceTaxRate?: number | null;
  discountPriceTaxRate?: number | null;
  costPriceTaxRate?: number | null;
  unit?: string;
  unitId?: string | null;
  userId?: string;
}

interface ProductDetailPageProps {
  product: Product;
}

const ProductDetailPage: NextPage<ProductDetailPageProps> = ({ product }) => {
  return (
    <DashboardLayout>
      <div className="w-full px-4 py-6">
        {/* Header with back button and actions */}
        <ProductHeader
          productId={product.id}
          productName={product.name}
          isDraft={product.isDraft}
        />

        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column - Image and summary */}
          <div className="lg:col-span-1 space-y-6">
            <ProductSummary
              name={product.name}
              image={product.image}
              price={product.price}
              wholesalePrice={product.wholesalePrice}
              discountPrice={product.discountPrice} // Pass discountPrice
              cost={product.cost}
              stock={product.stock}
              minStockLevel={product.minStockLevel}
              category={product.category}
            />
          </div>

          {/* Right column - Tabs with details */}
          <div className="lg:col-span-2">
            <ProductTabs
              id={product.id}
              name={product.name}
              sku={product.sku}
              description={product.description}
              barcode={product.barcode}
              taxRate={product.taxRate}
              tags={product.tags}
              stock={product.stock}
              minStockLevel={product.minStockLevel}
              trackInventory={product.trackInventory}
              hasVariants={product.hasVariants}
              weight={product.weight}
              dimensions={product.dimensions}
              variants={product.variants}
            />
          </div>
        </div>

        {/* Transaction History - Full Width */}
        <ProductTransactionHistory productId={product.id} />

        {/* Timestamps */}
        <ProductTimestamps
          createdAt={product.createdAt}
          updatedAt={product.updatedAt}
        />
      </div>
    </DashboardLayout>
  );
};

export default ProductDetailPage;
