"use client";

import React from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ProductFormValues } from "../types";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CreditCard,
  Tag,
  Percent,
  Package,
  AlertCircle,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Helper function to allow only numbers and decimal point in input
const handleNumberInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  // Allow: backspace, delete, tab, escape, enter, decimal point, and numbers
  if (
    e.key === "Backspace" ||
    e.key === "Delete" ||
    e.key === "Tab" ||
    e.key === "Escape" ||
    e.key === "Enter" ||
    e.key === "." ||
    e.key === "," ||
    (e.key >= "0" && e.key <= "9")
  ) {
    // Allow comma as decimal separator but convert to period for value
    if (e.key === ",") {
      e.preventDefault();
      const input = e.target as HTMLInputElement;
      const start = input.selectionStart || 0;
      const end = input.selectionEnd || 0;
      const value = input.value;
      input.value = value.substring(0, start) + "." + value.substring(end);
      input.selectionStart = input.selectionEnd = start + 1;
    }
    // Check if decimal point already exists when trying to add another
    if (e.key === "." && (e.target as HTMLInputElement).value.includes(".")) {
      e.preventDefault();
    }
    return;
  }
  // Block all other keys
  e.preventDefault();
};

// Format number with Indonesian format (using dots as thousand separators)
const formatToIDR = (value: string | number): string => {
  if (!value && value !== 0) return "";
  if (value === 0 || value === "0") return "";

  // Convert to string and remove any non-numeric characters except decimal point
  const numStr = String(value).replace(/[^\d.]/g, "");

  // Split by decimal point
  const parts = numStr.split(".");

  // Format the integer part with dots as thousand separators
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ".");

  // Return formatted number (with decimal part if exists)
  return parts.join(",");
};

// Parse formatted number back to raw value
const parseFromIDR = (formattedValue: string): string => {
  // Remove all dots and replace comma with dot for decimal
  return formattedValue.replace(/\./g, "").replace(",", ".");
};

interface ProductPricingInventoryProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
}

const ProductPricingInventory: React.FC<ProductPricingInventoryProps> = ({
  control,
  isPending,
}) => {
  // Calculate profit if both price and cost are set
  const price = parseFloat(control._formValues.price as string) || 0;
  const wholesalePrice =
    parseFloat(control._formValues.wholesalePrice as string) || undefined;
  const discountPrice =
    parseFloat(control._formValues.discountPrice as string) || undefined;
  const cost = parseFloat(control._formValues.cost as string) || 0;

  // Get individual tax rates
  const salePriceTaxRate =
    parseFloat(control._formValues.salePriceTaxRate as string) || 0;
  const wholesalePriceTaxRate =
    parseFloat(control._formValues.wholesalePriceTaxRate as string) || 0;
  const discountPriceTaxRate =
    parseFloat(control._formValues.discountPriceTaxRate as string) || 0;
  const costPriceTaxRate =
    parseFloat(control._formValues.costPriceTaxRate as string) || 0;

  // Use discount price for calculations if available, otherwise use wholesale or regular price
  const effectivePrice =
    discountPrice !== undefined
      ? discountPrice
      : wholesalePrice !== undefined
        ? wholesalePrice
        : price;
  const effectiveTaxRate =
    discountPrice !== undefined
      ? discountPriceTaxRate
      : wholesalePrice !== undefined
        ? wholesalePriceTaxRate
        : salePriceTaxRate;

  // Calculate profit (considering taxes)
  const priceAfterTax = effectivePrice * (1 + effectiveTaxRate / 100);
  const costAfterTax = cost * (1 + costPriceTaxRate / 100);
  const profitAmount =
    effectivePrice > 0 && cost > 0 ? priceAfterTax - costAfterTax : 0;
  const profitPercentage =
    costAfterTax > 0 ? (profitAmount / costAfterTax) * 100 : 0;

  // Determine profit status
  const isProfitable = profitAmount > 0;
  const profitStatus = isProfitable
    ? "positive"
    : profitAmount < 0
      ? "negative"
      : "neutral";

  return (
    <div className="space-y-6">
      {/* Pricing Section */}
      <Card className="overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center gap-2 text-gray-900 dark:text-gray-100">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Tag className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            Informasi Harga
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Table Layout for Pricing */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-200 dark:border-gray-700">
                  <TableHead className="text-left font-semibold text-gray-700 dark:text-gray-300">
                    Jenis Harga
                  </TableHead>
                  <TableHead className="text-left font-semibold text-gray-700 dark:text-gray-300">
                    Nilai (Rp)
                  </TableHead>
                  <TableHead className="text-left font-semibold text-gray-700 dark:text-gray-300">
                    Tarif Pajak (%)
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* Harga Jual Row */}
                <TableRow className="border-gray-200 dark:border-gray-700">
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-green-100 dark:bg-green-900/30 rounded-md">
                        <CreditCard className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Harga Jual Satuan{" "}
                        <span className="text-red-500 text-xs">*</span>
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                Rp
                              </span>
                            </div>
                            <FormControl className="form-control">
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                name="price"
                                value={formatToIDR(field.value)}
                                onFocus={() => {
                                  if (String(field.value) === "0") {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  if (!field.value && field.value !== 0) {
                                    field.onChange("0");
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  const rawValue = parseFromIDR(e.target.value);
                                  const parts = rawValue.split(".");
                                  const sanitized =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  field.onChange(sanitized || "0");
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="salePriceTaxRate"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                %
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                value={
                                  String(field.value) === "0"
                                    ? ""
                                    : String(field.value)
                                }
                                onFocus={() => {
                                  if (String(field.value) === "0") {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  if (!field.value && field.value !== 0) {
                                    field.onChange("0");
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  let value = e.target.value.replace(
                                    /[^0-9.]/g,
                                    ""
                                  );
                                  const parts = value.split(".");
                                  value =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  const numValue = parseFloat(value);
                                  if (!isNaN(numValue) && numValue > 100) {
                                    value = "100";
                                  }
                                  field.onChange(value || "0");
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                </TableRow>

                {/* Harga Grosir Row */}
                <TableRow className="border-gray-200 dark:border-gray-700">
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-purple-100 dark:bg-purple-900/30 rounded-md">
                        <Package className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Harga Grosir{" "}
                        <span className="text-xs text-gray-400">
                          (Opsional)
                        </span>
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="wholesalePrice"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                Rp
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                name="wholesalePrice"
                                value={
                                  field.value === undefined
                                    ? ""
                                    : formatToIDR(field.value)
                                }
                                onFocus={() => {
                                  if (
                                    field.value === undefined ||
                                    String(field.value) === "0"
                                  ) {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  const stringValue = String(field.value);
                                  if (
                                    !field.value ||
                                    stringValue === "0" ||
                                    stringValue === ""
                                  ) {
                                    field.onChange(undefined);
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  const rawValue = parseFromIDR(e.target.value);
                                  if (!rawValue) {
                                    field.onChange(undefined);
                                    return;
                                  }
                                  const parts = rawValue.split(".");
                                  const sanitized =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  field.onChange(sanitized);
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="wholesalePriceTaxRate"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                %
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                value={
                                  String(field.value) === "0"
                                    ? ""
                                    : String(field.value)
                                }
                                onFocus={() => {
                                  if (String(field.value) === "0") {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  if (!field.value && field.value !== 0) {
                                    field.onChange("0");
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  let value = e.target.value.replace(
                                    /[^0-9.]/g,
                                    ""
                                  );
                                  const parts = value.split(".");
                                  value =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  const numValue = parseFloat(value);
                                  if (!isNaN(numValue) && numValue > 100) {
                                    value = "100";
                                  }
                                  field.onChange(value || "0");
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                </TableRow>

                {/* Harga Diskon Row */}
                <TableRow className="border-gray-200 dark:border-gray-700">
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-orange-100 dark:bg-orange-900/30 rounded-md">
                        <TrendingDown className="h-3.5 w-3.5 text-orange-600 dark:text-orange-400" />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Harga Diskon{" "}
                        <span className="text-xs text-gray-400">
                          (Opsional)
                        </span>
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="discountPrice"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                Rp
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-orange-500 focus:ring-orange-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                name="discountPrice"
                                value={
                                  field.value === undefined
                                    ? ""
                                    : formatToIDR(field.value)
                                }
                                onFocus={() => {
                                  if (
                                    field.value === undefined ||
                                    String(field.value) === "0"
                                  ) {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  const stringValue = String(field.value);
                                  if (
                                    !field.value ||
                                    stringValue === "0" ||
                                    stringValue === ""
                                  ) {
                                    field.onChange(undefined);
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  const rawValue = parseFromIDR(e.target.value);
                                  if (!rawValue) {
                                    field.onChange(undefined);
                                    return;
                                  }
                                  const parts = rawValue.split(".");
                                  const sanitized =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  field.onChange(sanitized);
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="discountPriceTaxRate"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                %
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-orange-500 focus:ring-orange-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                value={
                                  String(field.value) === "0"
                                    ? ""
                                    : String(field.value)
                                }
                                onFocus={() => {
                                  if (String(field.value) === "0") {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  if (!field.value && field.value !== 0) {
                                    field.onChange("0");
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  let value = e.target.value.replace(
                                    /[^0-9.]/g,
                                    ""
                                  );
                                  const parts = value.split(".");
                                  value =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  const numValue = parseFloat(value);
                                  if (!isNaN(numValue) && numValue > 100) {
                                    value = "100";
                                  }
                                  field.onChange(value || "0");
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                </TableRow>

                {/* Harga Beli Row */}
                <TableRow className="border-gray-200 dark:border-gray-700">
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                        <TrendingUp className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Harga Beli{" "}
                        <span className="text-xs text-gray-400">
                          (Opsional)
                        </span>
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="cost"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                Rp
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                name="cost"
                                value={formatToIDR(field.value)}
                                onFocus={() => {
                                  if (String(field.value) === "0") {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  if (!field.value && field.value !== 0) {
                                    field.onChange("0");
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  const rawValue = parseFromIDR(e.target.value);
                                  const parts = rawValue.split(".");
                                  const sanitized =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  field.onChange(sanitized || "0");
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                  <TableCell className="py-4">
                    <FormField
                      control={control}
                      name="costPriceTaxRate"
                      render={({ field }) => (
                        <FormItem>
                          <div className="relative group">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium px-2 py-1 rounded-md">
                                %
                              </span>
                            </div>
                            <FormControl>
                              <Input
                                type="text"
                                className="pl-16 pr-4 h-12 text-lg font-semibold border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 bg-white dark:bg-gray-800"
                                placeholder="0"
                                value={
                                  String(field.value) === "0"
                                    ? ""
                                    : String(field.value)
                                }
                                onFocus={() => {
                                  if (String(field.value) === "0") {
                                    field.onChange("");
                                  }
                                }}
                                onBlur={() => {
                                  if (!field.value && field.value !== 0) {
                                    field.onChange("0");
                                  }
                                }}
                                onKeyDown={handleNumberInput}
                                onChange={(e) => {
                                  let value = e.target.value.replace(
                                    /[^0-9.]/g,
                                    ""
                                  );
                                  const parts = value.split(".");
                                  value =
                                    parts[0] +
                                    (parts.length > 1
                                      ? "." + parts.slice(1).join("")
                                      : "");
                                  const numValue = parseFloat(value);
                                  if (!isNaN(numValue) && numValue > 100) {
                                    value = "100";
                                  }
                                  field.onChange(value || "0");
                                }}
                                disabled={isPending}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </CardContent>

        {/* Interactive Profit Calculation */}
        {cost > 0 && price > 0 && (
          <div className="mt-6 p-4 m-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <TrendingUp
                    className={`h-5 w-5 ${
                      profitStatus === "positive"
                        ? "text-green-600 dark:text-green-400"
                        : profitStatus === "negative"
                          ? "text-red-600 dark:text-red-400"
                          : "text-gray-600 dark:text-gray-400"
                    }`}
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    Estimasi Keuntungan
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {discountPrice
                      ? "Berdasarkan harga diskon"
                      : wholesalePrice
                        ? "Berdasarkan harga grosir"
                        : "Berdasarkan harga jual satuan"}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p
                      className={`text-lg font-bold ${
                        profitStatus === "positive"
                          ? "text-green-600 dark:text-green-400"
                          : profitStatus === "negative"
                            ? "text-red-600 dark:text-red-400"
                            : "text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      Rp {formatToIDR(profitAmount)}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      per unit
                    </p>
                  </div>
                  {profitAmount !== 0 && (
                    <Badge
                      variant="outline"
                      className={`px-3 py-1 text-sm font-semibold ${
                        profitStatus === "positive"
                          ? "bg-green-100 text-green-700 border-green-300 dark:bg-green-900/30 dark:text-green-400 dark:border-green-600"
                          : profitStatus === "negative"
                            ? "bg-red-100 text-red-700 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-600"
                            : "bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600"
                      }`}
                    >
                      {profitStatus === "positive" ? "+" : ""}
                      {profitPercentage.toFixed(1)}%
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ProductPricingInventory;
